"use client";

import { useMap } from "@geon-map/react-odf";
import {
  APIResponseType,
  createGeonAddrgeoClient,
  crtfckey,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import {
  ContextMenu,
  ContextMenuCheckboxItem,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuLabel,
  ContextMenuSeparator,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
  ContextMenuTrigger,
} from "@geon-ui/react/primitives/context-menu";
import { Dialog, DialogTrigger } from "@geon-ui/react/primitives/dialog";
import { type AppConfig, useTranslations } from "next-intl";
import React from "react";
import { toast } from "sonner";

import EstateDialogContent from "./estate/dialog-content";

type CopyType = keyof AppConfig["Messages"]["copy"]["types"];

export default function ServiceContextMenu({
  children,
}: {
  children: React.ReactNode;
}) {
  // message handler
  const t = useTranslations("copy");
  const { map } = useMap();
  const client = createGeonAddrgeoClient();

  // 경위도
  const [coord, setCoord] = React.useState<{ lng: number; lat: number }>({
    lng: 0,
    lat: 0,
  });
  // Estate Dialog Tab
  const [tabValue, setTabValue] = React.useState<string>("land");

  // 경위도 검색 API query
  const { data } = useAppQuery<
    APIResponseType<(typeof client)["address"]["coord"]>
  >({
    queryKey: ["address/coord", coord],
    queryFn: () =>
      client.address.coord({
        lng: coord.lng,
        lat: coord.lat,
        crtfckey: crtfckey,
      }),
  });
  // 지번 주소
  const juso = React.useMemo(() => data?.result.jusoList[0]?.jibunAddr, [data]);
  // PNU
  const pnu = React.useMemo(() => data?.result.jusoList[0]?.addrPnu, [data]);

  // 우클릭 이벤트
  const onRightClick = React.useCallback(
    (event: any) => {
      // map 좌표
      const coord = map.getCoordinateFromPixel(event.pixel);
      // map 좌표 -> 경위도 변환
      const [lng, lat] = map.getProjection().unproject(coord, "4326");
      setCoord({ lng, lat });
    },
    [map],
  );
  // 클립보드 복사
  const copyClipboard = (content: string, type: CopyType) => {
    navigator.clipboard.writeText(content);
    toast.success(t("success", { type: t(`types.${type}`) }), {
      id: `copy-clipboard`,
    });
  };

  // 우클릭 이벤트 등록/해제
  React.useEffect(() => {
    // TODO: Map Event Handler
    if (map) {
      map.on("contextmenu", onRightClick);
    }

    return () => {
      if (map) {
        map.un("contextmenu", onRightClick);
      }
    };
  }, [map, onRightClick]);

  return (
    <Dialog>
      <ContextMenu>
        <ContextMenuTrigger className="size-full">
          {children}
        </ContextMenuTrigger>
        <ContextMenuContent className="w-52">
          <ContextMenuSub>
            <ContextMenuSubTrigger inset disabled={!pnu || !juso}>
              통합 행정 정보 조회
            </ContextMenuSubTrigger>
            <ContextMenuSubContent className="w-44">
              <DialogTrigger asChild>
                <ContextMenuItem onClick={() => setTabValue("land-basic")}>
                  토지 대장
                </ContextMenuItem>
              </DialogTrigger>
              <DialogTrigger asChild>
                <ContextMenuItem
                  onClick={() => setTabValue("registry-headings")}
                >
                  건축물 대장
                </ContextMenuItem>
              </DialogTrigger>
              <DialogTrigger asChild>
                <ContextMenuItem onClick={() => setTabValue("price-ind")}>
                  개별 주택 가격
                </ContextMenuItem>
              </DialogTrigger>
              <DialogTrigger asChild>
                <ContextMenuItem onClick={() => setTabValue("price-pclnd")}>
                  공시지가
                </ContextMenuItem>
              </DialogTrigger>
              <DialogTrigger asChild>
                <ContextMenuItem onClick={() => setTabValue("land-useplan")}>
                  토지 이용 계획
                </ContextMenuItem>
              </DialogTrigger>
            </ContextMenuSubContent>
          </ContextMenuSub>
          <ContextMenuSeparator />
          <ContextMenuLabel className="text-muted-foreground text-xs">
            클립보드 복사
          </ContextMenuLabel>
          <ContextMenuItem
            inset
            onClick={() =>
              copyClipboard(`${coord.lng}, ${coord.lat}}`, "coord")
            }
          >
            경위도
          </ContextMenuItem>
          <ContextMenuItem
            inset
            onClick={() =>
              juso
                ? copyClipboard(juso, "juso")
                : toast.error(t("fail", { type: t("types.juso") }), {
                    id: `copy-clipboard`,
                  })
            }
          >
            지번 주소
          </ContextMenuItem>
          <ContextMenuItem
            inset
            onClick={() =>
              pnu
                ? copyClipboard(pnu, "pnu")
                : toast.error(t("fail", { type: t("types.pnu") }), {
                    id: `copy-clipboard`,
                  })
            }
          >
            PNU
          </ContextMenuItem>
          <ContextMenuSeparator />
          <ContextMenuLabel className="text-muted-foreground text-xs">
            토글 메뉴
          </ContextMenuLabel>
          <ContextMenuCheckboxItem checked>
            Show Bookmarks
          </ContextMenuCheckboxItem>
        </ContextMenuContent>
      </ContextMenu>

      <EstateDialogContent
        pnu={pnu! /* 조회 메뉴는 pnu 없으면 disabled (접근 불가) */}
        tabValue={tabValue}
        setTabValue={setTabValue}
        juso={juso}
      />
    </Dialog>
  );
}
