"use client";

import { DialogContent } from "./dialog-content";
import { DialogFooter } from "./dialog-footer";
import { DialogHeader } from "./dialog-header";
import { DraggableDialogRoot, useDialog } from "./draggable-dialog";

export type { DraggableDialogProps } from "./draggable-dialog";

// Compound 패턴으로 구성된 DraggableDialog
const DraggableDialog = Object.assign(DraggableDialogRoot, {
  Header: DialogHeader,
  Content: DialogContent,
  Footer: DialogFooter,
});

export { DraggableDialog, useDialog };
