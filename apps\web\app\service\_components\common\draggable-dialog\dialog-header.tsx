"use client";

import { useDraggable } from "@dnd-kit/core";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import { Minus, Square, X } from "lucide-react";

import { useDialog } from "./draggable-dialog";

interface DialogHeaderProps {
  children?: React.ReactNode;
  className?: string;
  icon?: React.ReactNode;
  showControls?: boolean;
  actions?: React.ReactNode;
}

export function DialogHeader({
  children,
  className,
  icon,
  showControls = true,
  actions,
}: DialogHeaderProps) {
  const { isMaximized, onMaximize, onMinimize, onClose, isDragging, dialogId } =
    useDialog();

  // 제목 영역에만 드래그 기능 적용
  const { attributes, listeners, setNodeRef } = useDraggable({
    id: dialogId,
    disabled: isMaximized,
  });

  return (
    <div
      className={cn(
        // Windows Edge 헤더 스타일
        "flex h-8 items-center justify-between",
        "border-b border-gray-200 bg-gray-50",
        "select-none", // 드래그 시 텍스트 선택 방지
        // 드래그 상태
        isDragging && "bg-gray-100",
        // 최대화 상태
        isMaximized && "bg-white",
        className,
      )}
    >
      {/* 왼쪽: 액션 버튼들 */}
      <div className="pointer-events-auto flex items-center gap-1">
        {actions}
      </div>

      {/* 가운데: 드래그 가능한 제목 영역 */}
      <div
        ref={setNodeRef}
        {...(!isMaximized ? { ...attributes, ...listeners } : {})}
        className={cn(
          "flex flex-1 items-center justify-center gap-2",
          "cursor-move", // 드래그 커서
          isMaximized && "cursor-default",
        )}
      >
        {icon && (
          <div className="flex h-4 w-4 flex-shrink-0 items-center justify-center">
            {icon}
          </div>
        )}
        <div className="whitespace-nowrap text-sm font-normal text-gray-800">
          {children}
        </div>
      </div>

      {/* 오른쪽: 윈도우 컨트롤 버튼들 */}
      {showControls && (
        <div className="pointer-events-auto flex items-center">
          {/* 최소화 버튼 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onMinimize();
            }}
            className={cn(
              "h-8 w-8 rounded-none border-none p-0",
              "hover:bg-gray-200 active:bg-gray-300",
              "transition-colors duration-75",
              "pointer-events-auto", // 명시적으로 클릭 이벤트 허용
            )}
            aria-label="최소화"
          >
            <Minus className="h-3 w-3" strokeWidth={1.5} />
          </Button>

          {/* 최대화/복원 버튼 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onMaximize();
            }}
            className={cn(
              "h-8 w-8 rounded-none border-none p-0",
              "hover:bg-gray-200 active:bg-gray-300",
              "transition-colors duration-75",
              "pointer-events-auto", // 명시적으로 클릭 이벤트 허용
            )}
            aria-label={isMaximized ? "복원" : "최대화"}
          >
            <Square className="h-1 w-1" strokeWidth={1.5} />
          </Button>

          {/* 닫기 버튼 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClose();
            }}
            className={cn(
              "h-8 w-8 rounded-none border-none p-0",
              "hover:bg-red-500 hover:text-white",
              "active:bg-red-600 active:text-white",
              "transition-colors duration-75",
              "pointer-events-auto", // 명시적으로 클릭 이벤트 허용
            )}
            aria-label="닫기"
          >
            <X className="h-4 w-4" strokeWidth={2} />
          </Button>
        </div>
      )}
    </div>
  );
}
