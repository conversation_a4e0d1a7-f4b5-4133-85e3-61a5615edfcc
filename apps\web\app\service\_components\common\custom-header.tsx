import { Button } from "@geon-ui/react/primitives/button";
import { BarChart3, FileSpreadsheet, Plus, Trash2 } from "lucide-react";
import { useState } from "react";

import type { FacilityDetailData } from "../../_types/facility-detail";
import { ServiceModal } from "./service-modal";

export function CustomHeader() {
  // const { selectedFacilities } = useFacilitySearch();
  const [selectedCount] = useState(0); // 실제로는 선택된 시설물 수
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);

  const handleRegisterFacility = () => {
    setIsRegisterModalOpen(true);
  };

  const handleRegisterModalClose = () => {
    setIsRegisterModalOpen(false);
  };

  const handleRegistered = (data: FacilityDetailData) => {
    console.log("시설물 등록 완료:", data);
    alert(`${data.facilityName || "시설물"}이 등록되었습니다.`);
    // TODO: 검색 결과 새로고침
  };

  const handleExcelDownload = () => {
    // TODO: 전체 결과 엑셀 다운로드 API 호출
    console.log("엑셀 다운로드");
  };

  const handleDeleteSelected = () => {
    // TODO: 선택된 시설물 삭제
    console.log("선택 삭제");
  };

  const handleStatistics = () => {
    // TODO: 통계 보기
    console.log("통계 보기");
  };

  return (
    <div className="border-b border-gray-200 py-2">
      <div className="flex items-center justify-end">
        {/* 우측: 액션 버튼들 */}
        <div className="flex items-center gap-2">
          {/* 통계 버튼 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleStatistics}
            className="text-gray-600 hover:text-gray-800"
          >
            <BarChart3 className="mr-1 h-4 w-4" />
            통계
          </Button>

          {/* 엑셀 다운로드 버튼 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleExcelDownload}
            className="text-green-600 hover:bg-green-50 hover:text-green-800"
          >
            <FileSpreadsheet className="mr-1 h-4 w-4" />
            엑셀 다운로드
          </Button>

          {/* 선택 삭제 버튼 (선택된 항목이 있을 때만) */}
          {selectedCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleDeleteSelected}
              className="text-red-600 hover:bg-red-50 hover:text-red-800"
            >
              <Trash2 className="mr-1 h-4 w-4" />
              삭제 ({selectedCount})
            </Button>
          )}

          {/* 시설물 등록 버튼 */}
          <Button
            size="sm"
            onClick={handleRegisterFacility}
            className="bg-blue-600 text-white hover:bg-blue-700"
          >
            <Plus className="mr-1 h-4 w-4" />
            등록
          </Button>
        </div>
      </div>

      {/* 시설물 등록 모달 */}
      <ServiceModal
        serviceName="road" // 현재는 road 서비스로 고정, 향후 동적으로 설정 가능
        isOpen={isRegisterModalOpen}
        onClose={handleRegisterModalClose}
        mode="register"
        facilityType="도로"
        onRegistered={handleRegistered}
      />
    </div>
  );
}
