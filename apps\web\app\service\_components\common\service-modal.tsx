"use client";

import { Alert, AlertDescription } from "@geon-ui/react/primitives/alert";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { AlertCircle } from "lucide-react";
import {
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import type {
  FacilityDetailData,
  FacilityDetailModalProps,
} from "../../_types/facility-detail";
import { DraggableDialog } from "./draggable-dialog";
import { loadServiceComponents } from "./service-registry";

/**
 * 서비스별 동적 모달 컴포넌트
 */
export function ServiceModal({
  isOpen,
  onClose,
  mode,
  facilityData,
  facilityType,
  serviceName = "road", // 기본값으로 road 사용
  onRegistered,
}: FacilityDetailModalProps & { serviceName?: string }) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<FacilityDetailData | undefined>(
    facilityData,
  );
  const [isEditing, setIsEditing] = useState(false);

  // 편집 폼 참조 (헤더 버튼에서 폼 제출하기 위해)
  const formRef = useRef<{ submit: () => void } | null>(null);

  // 서비스 컴포넌트 동적 로딩
  const serviceComponents = useMemo(() => {
    return loadServiceComponents(serviceName);
  }, [serviceName]);

  /**
   * 시설물 상세 정보 로딩
   */
  const loadFacilityDetail = useCallback(
    async (facilityId: string) => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/${serviceName}/${facilityId}`);
        if (!response.ok) {
          throw new Error("상세 정보를 불러올 수 없습니다");
        }

        const result = await response.json();
        setData(result.data);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "데이터 로딩 실패";
        setError(errorMessage);
        console.error("상세 정보 로딩 오류:", err);
      } finally {
        setLoading(false);
      }
    },
    [serviceName],
  );

  // 모달이 열릴 때 데이터 로딩
  useEffect(() => {
    if (!isOpen) {
      setError(null);
      setData(undefined);
      setIsEditing(false); // 모달 닫힐 때 편집 상태 초기화
      return;
    }

    // 등록 모드이거나 데이터가 이미 있는 경우는 로딩하지 않음
    if (mode === "register" || facilityData) {
      setData(facilityData);
      return;
    }

    // 상세 모드에서 데이터 로딩 필요한 경우
    if (mode === "detail" && facilityData && "facilityId" in facilityData) {
      loadFacilityDetail((facilityData as any).facilityId);
    }
  }, [isOpen, mode, facilityData, loadFacilityDetail]);

  /**
   * 등록 완료 처리
   */
  const handleRegisterSubmit = useCallback(
    async (formData: FacilityDetailData) => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/${serviceName}/register`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...formData,
            facilityType,
          }),
        });

        if (!response.ok) {
          throw new Error("등록에 실패했습니다");
        }

        const result = await response.json();

        // 등록 완료 콜백 호출
        onRegistered?.(result.data);

        // 모달 닫기
        onClose();
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "등록 실패";
        setError(errorMessage);
        console.error("등록 오류:", err);
      } finally {
        setLoading(false);
      }
    },
    [serviceName, facilityType, onRegistered, onClose],
  );

  /**
   * 편집 모드 전환
   */
  const handleEdit = useCallback(() => {
    setIsEditing(true);
  }, []);

  /**
   * 편집 취소
   */
  const handleEditCancel = useCallback(() => {
    setIsEditing(false);
    setError(null); // 편집 취소 시 에러 초기화
  }, []);

  /**
   * 편집 완료 처리
   */
  const handleEditSubmit = useCallback(
    async (formData: FacilityDetailData) => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(
          `/api/${serviceName}/${data?.facilityId}`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(formData),
          },
        );

        if (!response.ok) {
          throw new Error("수정에 실패했습니다");
        }

        const result = await response.json();
        setData(result.data);
        setIsEditing(false);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "수정 실패";
        setError(errorMessage);
        console.error("수정 오류:", err);
      } finally {
        setLoading(false);
      }
    },
    [serviceName, data?.facilityId],
  );

  /**
   * 다운로드 처리
   */
  const handleDownload = useCallback(() => {
    if (!data) return;

    // TODO: 실제 다운로드 API 호출
    console.log("다운로드:", data);
    alert("다운로드 기능이 구현될 예정입니다.");
  }, [data]);

  /**
   * 삭제 처리
   */
  const handleDelete = useCallback(async () => {
    if (!data?.facilityId) return;

    if (!confirm("정말로 삭제하시겠습니까?")) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/${serviceName}/${data.facilityId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("삭제에 실패했습니다");
      }

      alert("삭제되었습니다.");
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "삭제 실패";
      setError(errorMessage);
      console.error("삭제 오류:", err);
    } finally {
      setLoading(false);
    }
  }, [serviceName, data?.facilityId, onClose]);

  /**
   * 모달 제목 생성
   */
  const getModalTitle = () => {
    const serviceTypeText = facilityType || serviceName;

    if (isEditing) {
      return (
        <div className="flex items-center gap-2">{serviceTypeText} 수정</div>
      );
    }

    switch (mode) {
      case "detail":
        return (
          <div className="flex items-center gap-2">
            {serviceTypeText} 상세 정보
          </div>
        );
      case "register":
        return (
          <div className="flex items-center gap-2">{serviceTypeText} 등록</div>
        );
      default:
        return `${serviceTypeText} 정보`;
    }
  };

  /**
   * 액션 버튼들 렌더링
   */
  const renderActionButtons = () => {
    if (mode === "register" || !serviceComponents) return null;
    if (!data) return null;

    const { DetailHeader } = serviceComponents;

    return (
      <Suspense
        fallback={
          <div className="h-8 w-24 animate-pulse rounded bg-gray-200" />
        }
      >
        <DetailHeader
          onEdit={handleEdit}
          onDownload={handleDownload}
          onDelete={handleDelete}
          onSave={() => {
            // 폼 ref를 통해 제출
            if (formRef.current) {
              formRef.current.submit();
            }
          }}
          onCancel={handleEditCancel}
          loading={loading}
          isEditing={isEditing}
        />
      </Suspense>
    );
  };

  /**
   * 모달 콘텐츠 렌더링
   */
  const renderModalContent = () => {
    // 서비스 컴포넌트가 없는 경우
    if (!serviceComponents) {
      return (
        <div className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              지원하지 않는 서비스입니다: {serviceName}
            </AlertDescription>
          </Alert>
        </div>
      );
    }

    // 로딩 중
    if (loading) {
      return (
        <div className="space-y-4 p-6">
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-4 w-3/4" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/3" />
            <Skeleton className="h-4 w-2/3" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-4 w-1/2" />
          </div>
          <Skeleton className="h-20 w-full" />
        </div>
      );
    }

    // 에러 상태
    if (error) {
      return (
        <div className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      );
    }

    const { DetailView, DetailForm } = serviceComponents;

    // 편집 모드인 경우
    if (isEditing && data) {
      return (
        <Suspense fallback={<div className="p-6">로딩 중...</div>}>
          <DetailForm
            initialData={data}
            facilityType={facilityType}
            onSubmit={handleEditSubmit}
            onCancel={handleEditCancel}
            loading={loading}
          />
        </Suspense>
      );
    }

    // 모드별 컴포넌트 렌더링
    switch (mode) {
      case "detail":
        if (!data) {
          return (
            <div className="p-6">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>표시할 정보가 없습니다.</AlertDescription>
              </Alert>
            </div>
          );
        }
        return (
          <Suspense fallback={<div className="p-6">로딩 중...</div>}>
            <DetailView data={data} loading={loading} />
          </Suspense>
        );

      case "register":
        return (
          <Suspense fallback={<div className="p-6">로딩 중...</div>}>
            <DetailForm
              facilityType={facilityType}
              onSubmit={handleRegisterSubmit}
              onCancel={onClose}
              loading={loading}
            />
          </Suspense>
        );

      default:
        return null;
    }
  };

  return (
    <DraggableDialog
      isOpen={isOpen}
      onClose={onClose}
      dialogId={`${serviceName}-detail-modal`}
      className="w-auto min-w-[600px] max-w-[800px]"
    >
      <DraggableDialog.Header
        icon={<AlertCircle className="h-4 w-4 text-blue-500" />}
        actions={renderActionButtons()}
      >
        {getModalTitle()}
      </DraggableDialog.Header>

      <DraggableDialog.Content padding={false}>
        {renderModalContent()}
      </DraggableDialog.Content>

      <DraggableDialog.Footer>
        <div className="flex w-full items-center justify-between">
          <span>{serviceName} 관리 시스템</span>
          <span>{new Date().toLocaleDateString()}</span>
        </div>
      </DraggableDialog.Footer>
    </DraggableDialog>
  );
}

export default ServiceModal;
