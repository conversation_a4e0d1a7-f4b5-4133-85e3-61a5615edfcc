"use client";

import {
  type APIResponseType,
  createGeonMagpClient,
  type MagpClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import Link from "next/link";
import React from "react";

import { formatDate } from "@/app/board/_utils";

type Props = { nttId: string };

export default function AnnounceDetail({ nttId }: Props) {
  const client = createGeonMagpClient();
  const { data, isLoading, isError, error } = useAppQuery<
    APIResponseType<MagpClient["notice"]["select"]>
  >({
    queryKey: ["magp/notice/select", { nttId }],
    queryFn: () => client.notice.select({ nttId }),
    enabled: Boolean(nttId),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string")
    return (
      <div className="text-destructive flex justify-center align-middle">
        Error loading notice: {String(error)}
        {data && `, ${String(data.result as unknown as string)}`}
      </div>
    );

  const item = data.result;
  return (
    <div className="mx-auto w-full max-w-4xl space-y-6 p-4">
      <div className="flex items-start justify-between gap-4">
        <h1 className="text-xl font-semibold">{item.nttSj}</h1>
        <Link
          href="/board/announce"
          className="rounded-md border px-3 py-1 text-sm hover:bg-gray-50"
        >
          목록으로
        </Link>
      </div>

      <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
        <div>
          <span className="mr-2 font-medium">작성자</span>
          <span>{item.registerNm}</span>
        </div>
        <div>
          <span className="mr-2 font-medium">등록일</span>
          <span>{formatDate(item.registDt)}</span>
        </div>
        <div>
          <span className="mr-2 font-medium">조회수</span>
          <span>{item.nttRdcnt}</span>
        </div>
      </div>

      <div className="rounded-md border bg-white p-4">
        <div className="prose max-w-none whitespace-pre-wrap">{item.nttCn}</div>
      </div>
    </div>
  );
}
