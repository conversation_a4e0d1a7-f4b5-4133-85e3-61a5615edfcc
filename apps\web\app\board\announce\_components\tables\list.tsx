"use client";

import {
  type APIRequestType,
  type APIResponseType,
  createGeonMagpClient,
  type MagpClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { createColumnHelper } from "@tanstack/react-table";
import Link from "next/link";
import React from "react";

function formatDate(isoString: string): string {
  const date = new Date(isoString.slice(0, 23));
  const y = date.getFullYear();
  const m = String(date.getMonth() + 1).padStart(2, "0");
  const d = String(date.getDate()).padStart(2, "0");
  const hh = String(date.getHours()).padStart(2, "0");
  const mm = String(date.getMinutes()).padStart(2, "0");
  const ss = String(date.getSeconds()).padStart(2, "0");
  return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
}
import Pagination from "@/components/table/pagination";
import ViewTable from "@/components/table/view";

export default function List({
  ...props
}: APIRequestType<MagpClient["notice"]["list"]>) {
  const client = createGeonMagpClient();
  // Pagination States (map to magp: pageSize/pageIndex)
  const [numOfRows, setNumOfRows] = React.useState<number>(
    // default to 10 if not provided
    (props as any).pageSize ?? 10,
  );
  const [pageNo, setPageNo] = React.useState<number>(
    // default to 1 if not provided
    (props as any).pageIndex ?? 1,
  );

  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<MagpClient["notice"]["list"]>
  >({
    queryKey: [
      "magp/notice/list",
      { ...props, pageSize: numOfRows, pageIndex: pageNo },
    ],
    queryFn: () =>
      client.notice.list({
        ...(props as any),
        pageSize: numOfRows,
        pageIndex: pageNo,
      }),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string")
    return (
      <div className="text-destructive flex justify-center align-middle">
        Error loading notices: {error as string}
        {data && `, ${data?.result as unknown as string}`}
      </div>
    );

  const helper = createColumnHelper<(typeof data.result.resultList)[0]>();
  const columns = [
    helper.accessor("nttSj", {
      cell: (info) => {
        const row = info.row.original;
        return (
          <Link
            href={`/board/announce/${row.nttId}`}
            className="text-blue-600 hover:underline"
          >
            {info.getValue()}
          </Link>
        );
      },
      header: "제목",
    }),
    helper.accessor("registerNm", {
      cell: (info) => info.getValue(),
      header: "작성자",
    }),
    helper.accessor("registDt", {
      cell: (info) => formatDate(info.getValue()),
      header: "등록일",
    }),
    helper.accessor("nttRdcnt", {
      cell: (info) => info.getValue(),
      header: "조회수",
    }),
  ];

  return (
    <div className="flex w-full max-w-[1500px] flex-col overflow-hidden overflow-y-auto">
      <ViewTable data={data.result["resultList"]} columns={columns} pinHeader />
      {typeof data?.result !== "string" && data?.result.pageInfo && (
        <Pagination
          pageInfo={data.result.pageInfo}
          onPageNoChange={setPageNo}
          onNumOfRowsChange={(newNumOfRows) => {
            setNumOfRows(newNumOfRows);
            setPageNo(1);
          }}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}
