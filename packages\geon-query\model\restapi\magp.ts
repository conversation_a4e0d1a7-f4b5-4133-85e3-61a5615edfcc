import type { API_TYPE } from "../utils/geonAPI";
import { apiHelper } from "../utils/geonAPI";
import {
  AnnounceDetailResponse,
  ElevationResponse,
  LineStringRequest,
  NoticeCUDRequest,
  NoticeCUDResponse,
  NoticeDeleteRequest,
  NoticeListRequest,
  NoticeListResponse,
  PointStringRequest,
} from "./type/magp-type";

const type: API_TYPE = "magp";

export interface MagpAPIConfig {
  baseUrl?: string;
  crtfckey?: string;
  timeout?: number;
}

export type MagpClient = ReturnType<typeof createGeonMagpClient>;
export function createGeonMagpClient(config: MagpAPIConfig = {}) {
  const { baseUrl, crtfckey } = config;

  const api = apiHelper({ type, baseUrl, crtfckey });

  return {
    elevation: {
      point: api.post<PointStringRequest, ElevationResponse>(
        "/elevation/point",
      ),
      line: api.post<LineStringRequest, ElevationResponse>("/elevation/line"),
    },
    notice: {
      list: api.get<NoticeListRequest, NoticeListResponse>("/notice/list"),
      select: api.get<{ nttId: string }, AnnounceDetailResponse>(
        "/notice/select",
      ),
      delete: api.post<NoticeDeleteRequest, NoticeCUDResponse>(
        "/notice/delete",
      ),
      insert: api.post<NoticeCUDRequest, NoticeCUDResponse>("/notice/insert"),
      update: api.post<NoticeCUDRequest, NoticeCUDResponse>("/notice/update"),
    },
  };
}
