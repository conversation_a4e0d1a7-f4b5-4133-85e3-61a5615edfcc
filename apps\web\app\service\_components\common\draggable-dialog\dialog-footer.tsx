"use client";

import { cn } from "@geon-ui/react/lib/utils";

import { useDialog } from "./draggable-dialog";

interface DialogFooterProps {
  children?: React.ReactNode;
  className?: string;
}

export function DialogFooter({ children, className }: DialogFooterProps) {
  const { isMaximized } = useDialog();

  return (
    <div
      className={cn(
        // Windows Edge 푸터 스타일
        "flex h-6 items-center justify-between px-2 text-xs",
        "border-t border-gray-200 bg-gray-50 text-gray-600",
        "select-none",
        // 최대화 상태
        isMaximized && "bg-white",
        className,
      )}
    >
      {children}
    </div>
  );
}
