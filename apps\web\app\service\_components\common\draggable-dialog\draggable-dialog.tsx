"use client";

import { DndContext } from "@dnd-kit/core";
import { restrictToWindowEdges } from "@dnd-kit/modifiers";
import { cn } from "@geon-ui/react/lib/utils";
import {
  createContext,
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";

// Dialog Context
interface DialogContextValue {
  isMaximized: boolean;
  isMinimized: boolean;
  onMaximize: () => void;
  onMinimize: () => void;
  onClose: () => void;
  isDragging: boolean;
  dialogId: string;
}

const DialogContext = createContext<DialogContextValue | null>(null);

const useDialog = () => {
  const context = useContext(DialogContext);
  if (!context) {
    throw new Error(
      "Dialog compound components must be used within a DraggableDialog",
    );
  }
  return context;
};

// Dialog Root Component
interface DraggableDialogProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  dialogId?: string;
  className?: string;
  defaultPosition?: { x: number; y: number };
}

function DraggableDialogRoot({
  isOpen,
  onClose,
  children,
  dialogId = "dialog",
  className,
  defaultPosition = { x: 0, y: 0 },
}: DraggableDialogProps) {
  const [position, setPosition] = useState(defaultPosition);
  const [isMaximized, setIsMaximized] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragTransform, setDragTransform] = useState({ x: 0, y: 0 });
  const dialogRef = useRef<HTMLDivElement>(null);

  // ESC 키 처리
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        e.stopPropagation();
        onClose();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [isOpen, onClose]);

  // 위치 초기화
  useEffect(() => {
    if (!isOpen) {
      setPosition({ x: 0, y: 0 });
      setIsMaximized(false);
      setIsMinimized(false);
      setIsDragging(false);
    }
  }, [isOpen]);

  const handleMaximize = useCallback(() => {
    setIsMaximized((prev) => !prev);
    setIsMinimized(false);
  }, []);

  const handleMinimize = useCallback(() => {
    setIsMinimized((prev) => !prev);
    setIsMaximized(false);
  }, []);

  const handleDragStart = useCallback(() => {
    setIsDragging(true);
    setDragTransform({ x: 0, y: 0 });
  }, []);

  const handleDragMove = useCallback(
    (event: any) => {
      if (!isMaximized && event.delta) {
        setDragTransform({
          x: event.delta.x,
          y: event.delta.y,
        });
      }
    },
    [isMaximized],
  );

  const handleDragEnd = useCallback(
    (event: any) => {
      setIsDragging(false);
      if (event.delta && !isMaximized) {
        setPosition((prev) => ({
          x: prev.x + event.delta.x,
          y: prev.y + event.delta.y,
        }));
      }
      setDragTransform({ x: 0, y: 0 });
    },
    [isMaximized],
  );

  const contextValue: DialogContextValue = {
    isMaximized,
    isMinimized,
    onMaximize: handleMaximize,
    onMinimize: handleMinimize,
    onClose,
    isDragging,
    dialogId,
  };

  if (!isOpen) return null;

  const dialogStyle = {
    // 위치 및 변환만 인라인 스타일로 처리
    transform: isMaximized
      ? "none"
      : `translate3d(${position.x + dragTransform.x}px, ${position.y + dragTransform.y}px, 0)`,
    left: isMaximized ? "0" : "50%",
    top: isMaximized ? "0" : "50%",
  };

  return (
    <DialogContext.Provider value={contextValue}>
      <DndContext
        onDragStart={handleDragStart}
        onDragMove={handleDragMove}
        onDragEnd={handleDragEnd}
        modifiers={isMaximized ? [] : [restrictToWindowEdges]}
      >
        <DialogWrapper
          ref={dialogRef}
          style={dialogStyle}
          className={cn(
            // 기본 위치 및 레이어
            "fixed z-50",
            // 기본 크기 및 제약사항 (Tailwind로 처리)
            !isMaximized && [
              "-translate-x-1/2 -translate-y-1/2",
              "w-auto min-w-[400px] max-w-[90vw]",
              "h-fit max-h-[90vh] min-h-0",
            ],
            // 최대화 상태
            isMaximized && "h-screen w-screen",
            // 최소화 상태
            isMinimized && "hidden",
            // 사용자 정의 클래스 (모든 스타일 오버라이드 가능)
            className,
          )}
        >
          {children}
        </DialogWrapper>
      </DndContext>
    </DialogContext.Provider>
  );
}

// Dialog Wrapper (드래그 가능한 컨테이너)
interface DialogWrapperProps {
  children: React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
}

const DialogWrapper = forwardRef<HTMLDivElement, DialogWrapperProps>(
  ({ children, style, className }, ref) => {
    const { isMaximized, isDragging } = useDialog();

    const finalStyle = {
      ...style,
    };

    return (
      <div
        ref={ref}
        style={finalStyle}
        className={cn(
          // Windows Edge 스타일 베이스
          "border border-gray-300 shadow-2xl",
          "overflow-hidden rounded-lg",
          // 드래그 상태 효과
          isDragging && "shadow-3xl scale-[1.01]",
          // 최대화 상태
          isMaximized && "!rounded-none !border-none",
          className,
        )}
      >
        <div className="flex h-full min-h-0 flex-col">{children}</div>
      </div>
    );
  },
);

DialogWrapper.displayName = "DialogWrapper";

export { DraggableDialogRoot, useDialog };
export type { DraggableDialogProps };
